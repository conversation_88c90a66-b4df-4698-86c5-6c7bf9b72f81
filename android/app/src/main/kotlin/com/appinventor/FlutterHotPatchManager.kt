package com.appinventor


import android.annotation.SuppressLint
import android.content.Context
import android.content.res.AssetManager
import android.content.res.Resources
import android.content.res.loader.ResourcesLoader
import android.content.res.loader.ResourcesProvider
import android.os.Build
import android.os.ParcelFileDescriptor
import android.util.Log
import io.flutter.embedding.engine.FlutterShellArgs
import java.io.File

enum class SourceType(val folder: String) {
    Original("app_zip/zip/cocos"),
    Patch("files/patch");

    fun getRootFolder(context: Context, version: String? = null): File {
        val dataPath = context.filesDir.parentFile
        if (version == null) {
            return File(dataPath, folder)
        }
        return File(dataPath, "$folder/$version")
    }

    fun getSoFolder(context: Context, version: String? = null): File {
        return File(getRootFolder(context, version), "so")
    }

    fun getSoAbiFolder(context: Context, version: String? = null): File {
        val abi = Build.SUPPORTED_ABIS[0]
        return File(getSoFolder(context, version), abi)
    }

    fun getLibAppSoFile(context: Context, version: String? = null): File {
        return File(getSoAbiFolder(context, version), "libapp.so")
    }

    fun getApkFile(context: Context, version: String? = null): File {
        val APK_NAME = "cocos.apk"
        return File(getRootFolder(context, version), APK_NAME)
    }
}

/**
 * created by kexuejin 2025/5/14
 */
object FlutterHotPatchManager {
    private var isPatch = false


    @Throws(Exception::class)
    fun patchLibAppSo(context: Context, shellArgs: FlutterShellArgs, version: String) {
        val patchSoFile = SourceType.Patch.getLibAppSoFile(context, version)
        val patchOriginalFile = SourceType.Original.getLibAppSoFile(context)
        val appSoFile = if (patchSoFile.exists()) patchSoFile else patchOriginalFile
        if (!appSoFile.exists()) return
        Log.i("Patch", "patchSoPath: $appSoFile")
        shellArgs.add("--aot-shared-library-name=${appSoFile.absolutePath}")
    }

    @SuppressLint("SoonBlockedPrivateApi")
    fun patchAssets(context: Context, resources: Resources): AssetManager {
        val patchAssetFile = SourceType.Patch.getApkFile(context)
        if (!patchAssetFile.exists()) return resources.assets
        Log.i("Patch", "patchAssetPath: $patchAssetFile")
        if (isPatch) return resources.assets

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val resFile = patchAssetFile
                val pfd = ParcelFileDescriptor.open(resFile, ParcelFileDescriptor.MODE_READ_WRITE)
                val resProvider = ResourcesProvider.loadFromApk(pfd, null)
                val resLoader = ResourcesLoader().apply {
                    addProvider(resProvider)
                }
                resources.addLoaders(resLoader)
                isPatch = true
            } else {
                val assetManager = resources.assets
                val addAssetPath =
                    AssetManager::class.java.getDeclaredMethod("addAssetPath", String::class.java)
                addAssetPath.invoke(assetManager, patchAssetFile.absolutePath)
                isPatch = true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return resources.assets
    }
}
