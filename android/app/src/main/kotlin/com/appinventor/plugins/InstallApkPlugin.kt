package com.appinventor.plugins

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import android.util.Log
import androidx.core.content.FileProvider
import com.appinventor.FlutterHotPatchManager
import com.appinventor.HotFixPreference
import com.appinventor.SourceType
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream


class InstallApkPlugin : FlutterPlugin, MethodCallHandler {
    private var channel: MethodChannel? = null
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
        channel =
            MethodChannel(flutterPluginBinding.binaryMessenger, Channel.installApk.channelName)
        channel?.setMethodCallHandler(this)
    }

    override fun onDetachedFromEngine(binding: FlutterPluginBinding) {
        channel?.setMethodCallHandler(null)
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        if (call.method == "install") {
            val filePath = call.argument<String>("apkPath") ?: ""
            val version = call.argument<String>("version") ?: ""
            if (!TextUtils.isEmpty(filePath)) {
                installApk(File(filePath), result)
            } else {
                result.error("installApk", "apkPath is null", null);
            }
        } else if (call.method == "patch") {
            val filePath = call.argument<String>("apkPath") ?: ""
            val version = call.argument<String>("version") ?: ""
            if (!TextUtils.isEmpty(filePath)) {
                patchApk(File(filePath), version, result)
            } else {
                result.error("installApk", "apkPath is null", null);
            }
        } else if (call.method == "restartApp") {
            restartApp(result)
        } else {
            result.notImplemented()
        }
    }

    private fun installApk(apkFile: File, result: MethodChannel.Result) {
        if (apkFile.exists() && apkFile.length() > 0) {
            val intent: Intent
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                //AUTHORITY NEEDS TO BE THE SAME ALSO IN MANIFEST
                val apkUri =
                    FileProvider.getUriForFile(
                        context,
                        context.packageName + "." + "ota_update_provider",
                        apkFile
                    )
                intent = Intent(Intent.ACTION_INSTALL_PACKAGE)
                intent.setData(apkUri)
                intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            } else {
                val fileUri = Uri.fromFile(apkFile);
                intent = Intent(Intent.ACTION_VIEW)
                intent.setDataAndType(fileUri, "application/vnd.android.package-archive")
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent);
            result.success(true)
        } else {
            result.success(false)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun patchApk(apkFile: File, version: String, result: MethodChannel.Result) {
        if (!apkFile.exists() || apkFile.length() <= 0) {
            result.error("patchApk", "APK file does not exist or is empty", null)
            return
        }

        removeOldPatchFile()

        // Create the destination directories
        val soDestDir = SourceType.Patch.getSoFolder(context, version)
        val apkDestFile = SourceType.Patch.getApkFile(context, version)

        if (!soDestDir.exists()) {
            soDestDir.mkdirs()
        }

        if (apkDestFile.parentFile?.exists() == false) {
            apkDestFile.parentFile?.mkdirs()
        }

        GlobalScope.launch {
            try {
                // Extract the library files from the APK
                extractLibrariesFromApk(apkFile, soDestDir)

                // Copy the APK to the destination directory
                copyFile(apkFile, apkDestFile)

                HotFixPreference.setNewVersion(context, version)

                result.success(true)
            } catch (e: Exception) {
                soDestDir.deleteRecursively()
                Log.e("InstallApkPlugin", "Error patching APK: ${e.message}", e)
                result.error("patchApk", "Error patching APK: ${e.message}", null)
            }
        }
    }

    private fun removeOldPatchFile() {
        val allVersions = HotFixPreference.getAllVersions(context)
        val keepValidCount = 2 //  保留有效的patch文件数量
        var validCount = 0
        for (version in allVersions) {
            val isValid = HotFixPreference.isVersionValid(context, version)
            var isKeep = isValid

            if (isValid) {
                validCount++
                if (validCount > keepValidCount) {
                    isKeep = false
                }
            }

            if (!isKeep) {
                HotFixPreference.setVersionValid(context, version, false)
                val soDestDir = SourceType.Patch.getRootFolder(context, version)
                soDestDir.deleteRecursively()
            }
        }
    }

    /**
     * Extract library files from the APK to the specified directory
     */
    private suspend fun extractLibrariesFromApk(apkFile: File, destDir: File) {
        val buffer = ByteArray(8192)

        withContext(Dispatchers.IO) {
            ZipInputStream(BufferedInputStream(FileInputStream(apkFile))).use { zipIn ->
                var entry: ZipEntry? = zipIn.nextEntry

                while (entry != null) {
                    val entryName = entry.name

                    // Check if the entry is a library file
                    if (entryName.startsWith("lib/") && !entry.isDirectory) {
                        val fileName = entryName.substring(entryName.lastIndexOf('/') + 1)
                        val dirPath = entryName.substring("lib/".length, entryName.lastIndexOf('/'))

                        // Create the directory structure
                        val subDir = File(destDir, dirPath)
                        if (!subDir.exists()) {
                            subDir.mkdirs()
                        }

                        // Extract the file
                        val outFile = File(subDir, fileName)
                        BufferedOutputStream(FileOutputStream(outFile)).use { out ->
                            var len: Int
                            while (zipIn.read(buffer).also { len = it } > 0) {
                                out.write(buffer, 0, len)
                            }
                        }

                        Log.d(
                            "InstallApkPlugin",
                            "Extracted library: $entryName to ${outFile.absolutePath}"
                        )
                    }

                    zipIn.closeEntry()
                    entry = zipIn.nextEntry
                }
            }
        }
    }

    /**
     * Copy a file from source to destination
     */
    private suspend fun copyFile(sourceFile: File, destFile: File) {
        withContext(Dispatchers.IO) {
            if (destFile.parentFile?.exists() == false) {
                destFile.parentFile?.mkdirs()
            }
            FileInputStream(sourceFile).use { input ->
                FileOutputStream(destFile).use { output ->
                    val buffer = ByteArray(8192)
                    var len: Int
                    while (input.read(buffer).also { len = it } > 0) {
                        output.write(buffer, 0, len)
                    }
                }
            }
        }

        Log.d("InstallApkPlugin", "Copied APK to ${destFile.absolutePath}")
    }

    private fun restartApp(result: MethodChannel.Result) {
        runCatching {
            // https://github.com/gabrimatic/restart_app/blob/master/android/src/main/kotlin/gabrimatic/info/restart/RestartPlugin.kt
            context.startActivity(
                Intent.makeRestartActivityTask(
                    (context.packageManager.getLaunchIntentForPackage(
                        context.packageName
                    ))!!.component
                )
            )
            Runtime.getRuntime().exit(0)
        }.onFailure {
            result.error("", "", "")
        }.onSuccess {
            // 如果success，那么当然就重启应用了
            result.success(true)
        }
    }
}
