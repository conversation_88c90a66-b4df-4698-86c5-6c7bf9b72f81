package com.appinventor

import android.content.Context
import appinventor.ailiveschoolbd.GrammarTerms.TinkerLoadLibrary
import io.flutter.FlutterInjector
import io.flutter.embedding.engine.FlutterJNI
import java.io.File

/**
 * created by kexuejin 2025/5/14
 */
object FlutterManager {
    fun loadAndInitFlutter(context: Context, flutterSODir: File, appSOFile: File) {
        if (flutterSODir.exists()) {
            TinkerLoadLibrary.installNativeLibraryPath(
                context.classLoader, flutterSODir
            )
        }
        if (appSOFile.exists()) {
            // 这个只能调用一次
            try {
                FlutterInjector.setInstance(
                    FlutterInjector.Builder().setFlutterJNIFactory(
                        CustomFlutterJNI.CustomFactory(
                            appSOFile
                        )
                    ).build()
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    class CustomFlutterJNI(private val appSOFile: File) : FlutterJNI() {
        override fun init(
            context: Context,
            args: Array<out String>,
            bundlePath: String?,
            appStoragePath: String,
            engineCachesPath: String,
            initTimeMillis: Long
        ) {
            val hookArgs = args.toMutableList().run {
                add("--aot-shared-library-name=${appSOFile.absolutePath}")
                toTypedArray()
            }
            super.init(
                context, hookArgs, bundlePath, appStoragePath, engineCachesPath, initTimeMillis
            )
        }

        class CustomFactory(private val appSOFile: File) : Factory() {
            override fun provideFlutterJNI(): FlutterJNI {
                return CustomFlutterJNI(appSOFile)
            }
        }
    }
}
