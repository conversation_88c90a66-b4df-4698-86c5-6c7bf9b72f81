package com.appinventor

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.AssetManager
import android.os.Bundle
import com.appinventor.plugins.BackgroundTaskPlugin
import com.appinventor.plugins.InstallApkPlugin
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterShellArgs
import androidx.startup.AppInitializer
import androidx.work.WorkManagerInitializer

class MainActivity : FlutterActivity() {
    private val TAG = "MainActivity"
    override fun createPackageContext(packageName: String?, flags: Int): Context {
        return this
    }

    @SuppressLint("SoonBlockedPrivateApi")
    override fun getAssets(): AssetManager {
        return FlutterHotPatchManager.patchAssets(this, resources)
    }

    override fun getFlutterShellArgs(): FlutterShellArgs {
        val shellArgs = super.getFlutterShellArgs()
        // 查找可用的版本，并加载新的libapp.so，同时将现在用的版本设置为不可用
        val newVersionAvailable = HotFixPreference.getValidVersion(this)
        if (newVersionAvailable != null) {
            FlutterHotPatchManager.patchLibAppSo(this, shellArgs, newVersionAvailable)
            //设置当前版本
            HotFixPreference.setCurrentVersion(this, newVersionAvailable)
        }
        return shellArgs
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        try {
            flutterEngine.plugins.add(BackgroundTaskPlugin())
            flutterEngine.plugins.add(InstallApkPlugin())
        } catch (e: Exception) {
            io.flutter.Log.e(
                TAG,
                "Error registering plugin",
                e
            )
        }
    }

    override fun onFlutterUiDisplayed() {
        super.onFlutterUiDisplayed()
        // 如果启动成功，将新的版本设置为可用
        HotFixPreference.markCurrentVersionValid(context)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        FlutterManager.loadAndInitFlutter(
            this,
            SourceType.Original.getSoAbiFolder(this),
            SourceType.Original.getLibAppSoFile(
                this
            )
        )
        AppInitializer.getInstance(this.getApplicationContext())
            .initializeComponent(WorkManagerInitializer::class.java)
        super.onCreate(savedInstanceState)
    }

}