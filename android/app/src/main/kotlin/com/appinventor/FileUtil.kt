package com.appinventor

import android.content.res.AssetManager
import android.util.Log
import java.io.File
import java.io.FileOutputStream

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
class FileUtil {
    companion object {
        private val TAG = "FileUtil"

        fun copyFile(assets: AssetManager, name: String, toPath: String) {
            Log.d(TAG, "name:$name, toPath:$toPath")
            val input = assets.open(name)
            val file = File(toPath)
            if (file.parentFile?.exists() == false) {
                file.parentFile?.mkdirs()
            }
            val output = FileOutputStream(file)
            val buff = ByteArray(1024 * 1000)
            var len = -1
            input.use { reader ->
                output.use {
                    while ({ len = reader.read(buff); len }() != -1) {
                        it.write(buff, 0, len)
                    }
                }
            }

        }

        private fun copyAssetFolder(
            assetManager: AssetManager,
            srcFolder: String,
            destPath: String
        ) {
            try {
                val files = assetManager.list(srcFolder) ?: return
                File(destPath).mkdirs()

                for (file in files) {
                    val assetPath = if (srcFolder.isEmpty()) file else "$srcFolder/$file"
                    val destFilePath = "$destPath/$file"

                    // 检查是否是目录
                    val subFiles = assetManager.list(assetPath)
                    if (!subFiles.isNullOrEmpty()) {
                        // 是目录，递归复制
                        copyAssetFolder(assetManager, assetPath, destFilePath)
                    } else {
                        // 是文件，直接复制
                        copyFile(assetManager, assetPath, destFilePath)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error copying asset folder $srcFolder: ${e.message}")
            }
        }
    }
}