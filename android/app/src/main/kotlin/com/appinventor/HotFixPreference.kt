package com.appinventor

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit

object HotFixPreference {
    private const val PREF_NAME = "hotfix_prefs"
    private const val KEY_AVAILABLE_VERSION = "available_version"
    private const val KEY_CURRENT_VERSION = "current_version"

    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }

    fun setNewVersion(context: Context, version: String) {
        val allVersions = getAllVersions(context).toMutableSet()
        allVersions.add(version)
        getPrefs(context).edit { putStringSet(KEY_AVAILABLE_VERSION, allVersions) }
        //  set valid version to true
        setVersionValid(context, version, true)
    }

    fun setVersionValid(context: Context, version: String, valid: Boolean) {
        getPrefs(context).edit { putBoolean(version, valid) }
    }

    fun isVersionValid(context: Context, version: String): Bo<PERSON>an {
        return getPrefs(context).getBoolean(version, false)
    }

    fun getValidVersion(context: Context): String? {
        val allVersions = getAllVersions(context)
        //查找第一个可用的版本
        val firstAvailableVersion =
            allVersions.firstOrNull { getPrefs(context).getBoolean(it, false) }

        return firstAvailableVersion
    }

    fun getAllVersions(context: Context): Set<String> {
        val versions =
            getPrefs(context).getStringSet(KEY_AVAILABLE_VERSION, null)?.sortedWith { o1, o2 ->
                val o1s = o1.split(".")
                val o2s = o2.split(".")
                for (i in 0 until o1s.size.coerceAtMost(o2s.size)) {
                    val o1i = o1s[i].toInt()
                    val o2i = o2s[i].toInt()
                    if (o1i != o2i) {
                        return@sortedWith o2i - o1i
                    }
                }
                o1s.size - o2s.size
            }?.toSet()
        return versions ?: emptySet()
    }

    fun setCurrentVersion(context: Context, version: String) {
        getPrefs(context).edit { putString(KEY_CURRENT_VERSION, version) }
        setVersionValid(context, version, false)
    }

    fun markCurrentVersionValid(context: Context) {
        val currentVersion = getCurrentVersion(context)
        if (currentVersion != null) {
            setVersionValid(context, currentVersion, true)
        }
    }

    fun getCurrentVersion(context: Context): String? {
        return getPrefs(context).getString(KEY_CURRENT_VERSION, null)
    }
}