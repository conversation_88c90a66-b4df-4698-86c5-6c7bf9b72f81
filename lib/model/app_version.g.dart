// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_version.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppVersion _$AppVersionFromJson(Map<String, dynamic> json) => AppVersion(
      isIm: (json['is_im'] as num?)?.toInt() ?? 0,
      appVersion: json['app_version'] as String? ?? '',
      os: json['os'] as String? ?? '',
      isForce: (json['is_force'] as num?)?.toInt() ?? 0,
      updateDesc: json['update_desc'] as String? ?? '',
      downloadUrl: json['download_url'] as String? ?? '',
      arm64DownloadUrl: json['arm64_download_url'] as String? ?? '',
      armeabiDownloadUrl: json['armeabi_download_url'] as String? ?? '',
      arm64Size: (json['arm64_size'] as num?)?.toInt() ?? 0,
      armeabiSize: (json['armeabi_size'] as num?)?.toInt() ?? 0,
      totalPackageSize: (json['total_package_size'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] as String? ?? '',
    );

Map<String, dynamic> _$AppVersionToJson(AppVersion instance) =>
    <String, dynamic>{
      'is_im': instance.isIm,
      'app_version': instance.appVersion,
      'os': instance.os,
      'is_force': instance.isForce,
      'update_desc': instance.updateDesc,
      'download_url': instance.downloadUrl,
      'arm64_download_url': instance.arm64DownloadUrl,
      'armeabi_download_url': instance.armeabiDownloadUrl,
      'arm64_size': instance.arm64Size,
      'armeabi_size': instance.armeabiSize,
      'total_package_size': instance.totalPackageSize,
      'created_at': instance.createdAt,
    };
