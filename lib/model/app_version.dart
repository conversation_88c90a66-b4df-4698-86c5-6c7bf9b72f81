import 'package:json_annotation/json_annotation.dart';

part 'app_version.g.dart';

@JsonSerializable()
class  AppVersion {
  @Json<PERSON><PERSON>(name: 'is_im', defaultValue: 0)
  final int isIm;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_version', defaultValue: '')
  final String appVersion;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String os;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_force', defaultValue: 0)
  final int isForce;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'update_desc', defaultValue: '')
  final String updateDesc;
  @Json<PERSON>ey(name: 'download_url', defaultValue: '')
  final String downloadUrl;
  @Json<PERSON><PERSON>(name: 'arm64_download_url', defaultValue: '')
  final String arm64DownloadUrl;
  @Json<PERSON>ey(name: 'armeabi_download_url', defaultValue: '')
  final String armeabiDownloadUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'arm64_size', defaultValue: 0)
  final int arm64Size;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'armeabi_size', defaultValue: 0)
  final int armeabiSize;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'total_package_size', defaultValue: 0)
  final int totalPackageSize;
  @JsonKey(name: 'created_at', defaultValue: '')
  final String createdAt;

  const AppVersion({
    required this.isIm,
    required this.appVersion,
    required this.os,
    required this.isForce,
    required this.updateDesc,
    required this.downloadUrl,
    required this.arm64DownloadUrl,
    required this.armeabiDownloadUrl,
    required this.arm64Size,
    required this.armeabiSize,
    required this.totalPackageSize,
    required this.createdAt,
  });

  factory AppVersion.fromJson(Map<String, dynamic> json) =>
      _$AppVersionFromJson(json);

  Map<String, dynamic> toJson() => _$AppVersionToJson(this);
}
