import 'dart:io';

import 'package:and/model/app_version.dart';

import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

extension AppVersionExtension on AppVersion {
  String get fileName {
    return "gleezy_$appVersion.apk";
  }

  Future<File> get apkFile async {
    Directory dir = await getApplicationSupportDirectory();
    String savePath = p.join(dir.path, "ota_update", fileName);
    return File(savePath);
  }

  Future<int> get apkSize async {
    return totalPackageSize;
  }

  Future<bool> get isDownloadComplete async {
    var file = await apkFile;
    if (!file.existsSync()) return false;
    var totalPackageSize = await apkSize;
    if (totalPackageSize == 0) {
      return true;
    }
    return totalPackageSize == file.lengthSync();
  }

  Future deleteApkFile() async {
    File file = await apkFile;
    if (!file.existsSync()) {
      return;
    }
    file.delete();
  }

  Future deleteOldApk() async {
    // 删除除了当前版本的其他文件
    var apkFile = await this.apkFile;
    Directory directory = apkFile.parent;
    if (!directory.existsSync()) {
      return;
    }
    List<FileSystemEntity> list = directory.listSync();
    for (FileSystemEntity file in list) {
      if (file.path == apkFile.path) {
        continue;
      }
      file.deleteSync(recursive: true);
    }
  }
}
