import 'package:and/app.dart';
import 'package:and/cache/cache.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/im/background_task.dart';
import 'package:and/utils/install_referrer_utils.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:and/vest/period_tracker_app.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';

import 'common/environment/environment_config.dart';
import 'http/my_http.dart';
import 'io/common.dart';
import 'model/vestbag_config.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.

  print("Handling a background message: ${message.messageId}");
}

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding); // 保持原生启动页
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  var isIm = await initApp();
  FlutterNativeSplash.remove();

  InstallReferrerUtils.initReferrerDetails();
  if (isIm) {
    runApp(App());
  } else {
    runApp(PeriodTrackerApp());
  }

  setSystemUI();
}

Future<bool> initApp() async {
  await Cache.instance.init();

  if (EnvironmentConfig.isGooglePlay()) {
    var isIm = CacheHelper.isIM;
    if (!isIm) {
      try {
        VestbagConfig config = await HttpUtils.checkVestConfig();
        CacheHelper.saveIsIM(config.isIm == 1);
        if (config.isIm == 0) {
          return false;
        }
      } catch(e) {
        print(e);
        return false;
      }
    }
  }

  await CommonHelper.init();
  await FileUtils.init();

  BackgroundTask.init();
  await NotificationUtils.init();
  return true;
}

void setSystemUI() async {
  SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark);
  SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}
