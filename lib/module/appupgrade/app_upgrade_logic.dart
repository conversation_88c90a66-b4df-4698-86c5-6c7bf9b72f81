import 'dart:io';

import 'package:and/cache/cache_helper.dart';
import 'package:and/model/app_version.dart';
import 'package:and/model/extension/app_version_ext.dart';
import 'package:and/utils/common_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ota_update/ota_update.dart';
import 'package:url_launcher/url_launcher.dart';

import 'app_upgrade_utils.dart';

class AppUpgradeLogic extends GetxController {
  final isDownloading = false.obs;
  final currentEvent = Rx<OtaEvent?>(null);

  bool get isShowDownload =>
      currentEvent.value?.status == OtaStatus.DOWNLOADING;

  void updateApp(AppVersion appUpdate) async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      var downloadUrl = appUpdate.downloadUrl;
      if (downloadUrl.endsWith(".apk")) {
        downloadApk(appUpdate);
      } else {
        CommonHelper.launchInAppBrowser(
            CommonHelper.getFileUrl(appUpdate.downloadUrl),
            mode: LaunchMode.externalApplication);
      }
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      CommonHelper.launchAppStore();
    }
  }

  void downloadApk(AppVersion appUpdate, {bool isPatch = false}) async {
    try {
      var fileName = appUpdate.fileName;
      File file = await appUpdate.apkFile;
      var version = appUpdate.appVersion;
      //确保下载已完成
      if (await appUpdate.isDownloadComplete) {
        if (isPatch) {
          await AppUpgradeUtils.patchApk(file, version);
        } else {
          await AppUpgradeUtils.installApk(file, version);
        }
        return;
      }
      isDownloading.value = true;
      OtaUpdate()
          .execute(
        CommonHelper.getFileUrl(appUpdate.downloadUrl),
        // OPTIONAL
        destinationFilename: fileName,
      )
          .listen(
        (OtaEvent event) async {
          isDownloading.value = event.status == OtaStatus.DOWNLOADING;
          if (event.status == OtaStatus.CANCELED ||
              event.status == OtaStatus.DOWNLOAD_ERROR ||
              event.status == OtaStatus.CHECKSUM_ERROR) {
            file.delete();
          } else if (event.status == OtaStatus.INSTALLING) {
            var hasInstalled = await AppUpgradeUtils.hasInstallPermission();
            //如果没有安装权限，则进行patch安装
            if (!hasInstalled) {
              if (isPatch) {
                await AppUpgradeUtils.patchApk(file, version);
              } else {
                await AppUpgradeUtils.installApk(file, version);
              }
            }
          }
          currentEvent.value = event;
        },
      );
    } catch (e) {
      print('Failed to make OTA update. Details: $e');
    }
  }
}
