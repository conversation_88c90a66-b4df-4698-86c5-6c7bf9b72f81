import 'dart:async';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/eventbus/conversation_sync_event.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/entity/reminder.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import '../chat_page.dart';
import 'chat_scroll_controller.dart';
import 'in_memory_chat_controller.dart';

enum PullMode {
  /// 下拉刷新
  pullDown(0),

  /// 上拉加载
  pullUp(1);

  final int value;

  const PullMode(this.value);

  static PullMode fromValue(int value) {
    return PullMode.values.firstWhere((element) => element.value == value);
  }
}

class ChatMsgController extends GetxController {
  final ChatChannelArgument argument;

  String get channelID => argument.channelID;

  int get channelType => argument.channelType;

  late int tipsOrderSeq = argument.tipsOrderSeq;

  late final AppLifecycleListener _lifecycleListener;
  late StreamSubscription _conversationSyncEventSubscription;

  InMemoryChatController<WKMsg> messageController =
      InMemoryChatController<WKMsg>(equalityComparer: (a, b) {
    // 根据消息的clientMsgNo和orderSeq来判断消息是否重复
    return a.clientMsgNO == b.clientMsgNO;
  });
  RxList<WKMsg> messages = RxList<WKMsg>();
  RxList<WKReminder> reminders = RxList<WKReminder>();
  RxList<WKReminder> groupApproves = RxList<WKReminder>();

  ChatScrollController chatScrollController = ChatScrollController();

  final int pageSize = 30;

  var unreadCount = Rx<int>(0); // 未读消息数量
  var unreadStartMsgOrderSeq = 0; //新消息开始位置
  var lastPreviewMsgOrderSeq = 0; //上次浏览消息
  var keepOffsetY = 0; //上次浏览消息的偏移量
  var lastVisibleMsgSeq = 0; //最后可见消息序号
  var maxMsgSeq = 0; //最大消息序号
  var isSyncLastMsg = Rx<bool>(false);

  var isCanRefresh = Rx<bool>(true);
  var isCanLoadMore = Rx<bool>(false);

  var loadStatus = Rx<FitLoadStatus>(const FitLoading());
  var loadMode = Rx<PullMode>(PullMode.pullUp);

  var browseTo = Rx<int>(0);
  var draft = Rx<String>("");

  bool get isLoading => loadStatus.value is FitLoading;

  var keyMap = <int, GlobalKey>{};

  GlobalKey getKey(int index) {
    return keyMap.putIfAbsent(index, () => GlobalKey());
  }

  ChatMsgController({required this.argument}) {
    ever(messageController.messages, (value) {
      messages.value = value.reversed.toList();
    });
  }

  @override
  void onReady() {
    super.onReady();
    _conversationSyncEventSubscription =
        eventBus.on<ConversationSyncEvent>().listen((event) {
      if (event.conversation.channelId == channelID &&
          event.conversation.channelType == channelType) {
        var currentMaxSeq = event.conversation.lastMsgSeq;
        if (currentMaxSeq > maxMsgSeq) {
          loadFooterData(isForce: true);
        }
      }
    });

    startRefresh();
  }

  @override
  void onClose() {
    _conversationSyncEventSubscription.cancel();
    super.onClose();
  }

  Future<void> startRefresh() async {
    await EasyLoadingHelper.show(
        maskType: EasyLoadingMaskType.none,
        delay: Duration(milliseconds: 200),
        onAction: () async {
          await _doInit();
        });
  }

  Future<void> _doInit() async {
    //  初始化数据, 重置状态
    messageController.clear();
    unreadCount.value = 0;
    unreadStartMsgOrderSeq = 0;
    lastPreviewMsgOrderSeq = 0;
    keepOffsetY = 0;
    lastVisibleMsgSeq = 0;
    maxMsgSeq = 0;
    isSyncLastMsg.value = false;
    isCanRefresh.value = true;
    isCanLoadMore.value = false;

    var conversationMsg = await WKIM.shared.conversationManager
        .getUIMsgWithChannel(channelID, channelType);

    //未读消息数量
    unreadCount.value = conversationMsg?.unreadCount ?? 0;

    //获取最后一条消息
    WKMsg? msg;
    if (conversationMsg != null) {
      msg = await WKIM.shared.messageManager
          .getWithClientMsgNo(conversationMsg.clientMsgNo);
    }
    //最大消息序号
    maxMsgSeq = await WKIM.shared.messageManager
        .getMaxMessageSeq(channelID, channelType);

    if (tipsOrderSeq > 0) {
      print("tipsOrderSeq:$tipsOrderSeq");
    } else {
      if (unreadCount.value > 0) {
        // 未读消息开始位置
        int messageSeq = 0;
        if (msg != null) {
          if (msg.messageSeq == 0) {
            messageSeq = maxMsgSeq - unreadCount.value + 1;
          } else {
            messageSeq = msg.messageSeq - unreadCount.value + 1;
          }
        }
        unreadStartMsgOrderSeq = await WKIM.shared.messageManager
            .getMessageOrderSeq(messageSeq, channelID, channelType);
      } else {
        // 如果没有未读消息，则定位到上次浏览位置
        int keepMessageSeq =
            conversationMsg?.getRemoteMsgExtra()?.keepMessageSeq ?? 0;
        if (keepMessageSeq != 0) {
          lastPreviewMsgOrderSeq = await WKIM.shared.messageManager
              .getMessageOrderSeq(keepMessageSeq, channelID, channelType);
          keepOffsetY = conversationMsg?.getRemoteMsgExtra()?.keepOffsetY ?? 0;
        }
      }
    }

    //如果是定位到某个位置，则允许加载更多
    isCanLoadMore.value = lastPreviewMsgOrderSeq > 0;

    var aroundMsgSeq = 0;
    if (unreadStartMsgOrderSeq > 0) {
      aroundMsgSeq = unreadStartMsgOrderSeq;
      isCanLoadMore.value = true;
    }
    if (lastPreviewMsgOrderSeq != 0) {
      aroundMsgSeq = lastPreviewMsgOrderSeq;
    }
    if (tipsOrderSeq > 0) {
      aroundMsgSeq = tipsOrderSeq;
      isCanLoadMore.value = true;
    }
    // TODO：当消息只有一条的时候，会导致显示不出来
    if (aroundMsgSeq == 0) {
      if (msg != null) {
        aroundMsgSeq = msg.orderSeq;
      }
    }

    resetReminder();

    //首次加载
    bool isScrollToEnd =
        unreadStartMsgOrderSeq == 0 && lastPreviewMsgOrderSeq == 0;

    loadStatus.value = FitLoading();
    await _loadData(aroundMsgSeq,
        lastPreviewMsgOrderSeq == 0 ? PullMode.pullDown : PullMode.pullUp,
        isScrollToEnd: isScrollToEnd);

    WKConversationMsgExtra? extra = await WKIM.shared.conversationManager
        .getMsgExtraWithChannel(channelID, channelType);
    if (extra != null) {
      draft.value = extra.draft;
      browseTo.value = extra.browseTo;
    }
  }

  bool isNewLine(WKMsg msg) {
    return msg.orderSeq == unreadStartMsgOrderSeq;
  }

  /// 刷新数据
  Future<void> loadHeaderData({Function()? onStart}) async {
    if (!isCanRefresh.value || isLoading) return;

    loadStatus.value = FitLoading();
    onStart?.call();

    lastPreviewMsgOrderSeq = 0;
    await _loadData(0, PullMode.pullDown);

    /// 刷新完成
    print("refreshCompleted");
  }

  /// 加载更多
  void loadFooterData({Function()? onStart, bool isForce = false}) async {
    var canLoadMore = isForce || (isCanLoadMore.value && !isLoading);
    if (!canLoadMore) return;

    loadStatus.value = FitLoading();
    onStart?.call();

    lastPreviewMsgOrderSeq = 0;
    unreadStartMsgOrderSeq = 0;

    await _loadData(0, PullMode.pullUp);

    /// 加载完成
    print("loadComplete");
  }

  /// 跳转到最新的消息
  Future<void> jumpToLatest() async {
    if (isCanLoadMore.value) {
      isSyncLastMsg.value = true;
      unreadStartMsgOrderSeq = 0;
      lastPreviewMsgOrderSeq = 0;
      var maxOrderSeq = await WKIM.shared.messageManager
          .getMessageOrderSeq(0, channelID, channelType);
      try {
        await _loadData(maxOrderSeq, PullMode.pullDown,
            isScrollToEnd: true, isResetData: true);
        isCanLoadMore.value = false;
      } catch (e) {
        e.printError();
      } finally {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          isSyncLastMsg.value = false;
        });
      }
    } else {
      chatScrollController.jumpToBottom(delay: 0);
    }
  }

  /// 数据加载
  Future<bool> _loadData(int aroundMsgOrderSeq, PullMode pullMode,
      {bool isScrollToEnd = false, bool? isResetData}) async {
    print("loadData $aroundMsgOrderSeq $pullMode.");
    loadMode.value = pullMode;
    var isInit = isResetData ?? messages.isEmpty;
    try {
      var msgList = (await getMsgList(aroundMsgOrderSeq, pullMode));
      if (isResetData == true) {
        messageController.clear();
      }
      if (pullMode == PullMode.pullDown) {
        if (msgList.isEmpty) {
          isCanRefresh.value = false;
        }
        messageController.insertAll(msgList, index: 0);
      } else if (pullMode == PullMode.pullUp) {
        if (msgList.isEmpty) {
          isCanLoadMore.value = false;
        }
        messageController.insertAll(msgList);
      }
      loadStatus.value = FitLoadSuccess<bool>(msgList.isNotEmpty);

      //如果最后一条消息是当前会话的最大消息，则不允许加载更多
      int maxSeq = await WKIM.shared.messageManager
          .getMaxMessageSeq(channelID, channelType);
      if (messageController.lastOrNull()?.messageSeq == maxSeq) {
        isCanLoadMore.value = false;
      }
      if (isInit) {
        //跳转到未读的那条消息
        var msgOrderSeq = 0;
        double alignment = 0;
        if (unreadStartMsgOrderSeq != 0) {
          msgOrderSeq = unreadStartMsgOrderSeq;
          alignment = 1;
        } else if (lastPreviewMsgOrderSeq != 0) {
          msgOrderSeq = lastPreviewMsgOrderSeq;
        } else if (tipsOrderSeq != 0) {
          msgOrderSeq = tipsOrderSeq;
        }
        if (msgOrderSeq != 0) {
          var firstUnreadIndex =
              messages.indexWhere((e) => e.orderSeq == msgOrderSeq);
          if (firstUnreadIndex >= 0) {
            chatScrollController.jumpToPosition(firstUnreadIndex,
                delay: 0, alignment: alignment);
            tipsOrderSeq = 0;
          }
        } else {
          chatScrollController.jumpToBottom(delay: 0);
        }
      } else {
        if (isScrollToEnd) {
          chatScrollController.jumpToBottom(delay: 0);
        }
      }
    } catch (error) {
      print("出错 $error");
      loadStatus.value = const FitLoadError();
      return false;
    }

    return true;
  }

  Future<List<WKMsg>> getMsgList(
      int aroundMsgOrderSeq, PullMode pullMode) async {
    Completer<List<WKMsg>> completer = Completer<List<WKMsg>>();
    print("开始同步消息");
    int oldestOrderSeq = 0;
    var contain = false;
    if (pullMode == PullMode.pullUp) {
      oldestOrderSeq = messageController
              .lastOrNull(where: (e) => e.orderSeq != 0)
              ?.orderSeq ??
          0;
    } else {
      oldestOrderSeq = messageController
              .firstOrNull(where: (e) => e.orderSeq != 0)
              ?.orderSeq ??
          0;
    }
    if (isSyncLastMsg.value) {
      oldestOrderSeq = 0;
    }
    if (lastPreviewMsgOrderSeq > 0) {
      contain = true;
      oldestOrderSeq = lastPreviewMsgOrderSeq;
    }
    if (messages.isEmpty || lastPreviewMsgOrderSeq > 0) {
      contain = true;
    }
    print(
        "load message, pullMode:$pullMode, oldestOrderSeq:$oldestOrderSeq, contain:$contain, aroundMsgOrderSeq:$aroundMsgOrderSeq");
    WKIM.shared.messageManager.getOrSyncHistoryMessages(
        channelID,
        channelType,
        oldestOrderSeq,
        contain,
        pullMode.value,
        pageSize,
        aroundMsgOrderSeq, (msgList) async {
      print('同步完成${msgList.length}条消息');
      completer.complete(msgList);
    }, () {
      print('消息同步中');
    });
    return completer.future;
  }

  void resetReminder() async {
    var allReminders = await WKIM.shared.reminderManager
        .getWithChannel(channelID, channelType, 0);
    var loginUid = CacheHelper.uid ?? '';
    //获取当前未读的提醒
    reminders.value = allReminders.where((reminder) {
      var isPublisher =
          reminder.publisher.isNotEmpty && reminder.publisher == loginUid;
      return (reminder.type == WKMentionType.wkReminderTypeMentionMe &&
          !isPublisher);
    }).toList();
    //获取当前处理的approve
    groupApproves.value = allReminders.where((reminder) {
      return (reminder.type == WKMentionType.wkApplyJoinGroupApprove);
    }).toList();
  }
}
