import 'package:and/module/chat/menu/copy_menu_action.dart';
import 'package:and/module/chat/menu/delete_menu_action.dart';
import 'package:and/module/chat/menu/forward_menu_action.dart';
import 'package:and/module/chat/menu/multiple_choice_menu_action.dart';
import 'package:and/module/chat/menu/pin_menu_action.dart';
import 'package:and/module/chat/menu/recognize_menu_action.dart';
import 'package:and/module/chat/menu/reply_menu_action.dart';
import 'package:and/module/chat/menu/revoke_menu_action.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

import 'sticker_menu_action.dart';

enum MessageMenuType { copy, revoke, forward, recognize, reply, delete, multipleChoice, pin, sticker }

abstract class MessageMenuAction {
  MessageMenuType getType();

  MenuItem render(
      BuildContext context, WKMsg msg, Function(MessageMenuType) onTap);

  Future<bool> canRender(WKMsg msg);
}

class MessageMenuActionRegistry {
  static final MessageMenuActionRegistry _instance =
      MessageMenuActionRegistry._internal();

  factory MessageMenuActionRegistry() => _instance;

  MessageMenuActionRegistry._internal();

  final List<MessageMenuAction> _renderers = [
    ReplyMenuAction(),
    CopyMenuAction(),
    StickerMenuAction(),
    RecognizeMenuAction(),
    ForwardMenuAction(),
    MultipleChoiceMenuAction(),
    RevokeMenuAction(),
    DeleteMenuAction(),
    PinMenuAction()
  ];

  Future<List<MessageMenuAction>> getMenus(WKMsg msg) async {
    List<MessageMenuAction> menus = [];
    for (var renderer in _renderers) {
      if (await renderer.canRender(msg)) {
        menus.add(renderer);
      }
    }
    return menus;
  }
}
