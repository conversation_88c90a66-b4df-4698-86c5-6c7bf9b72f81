import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/model/pinned_message.dart';
import 'package:and/module/chat/widget/message/common/msg_image_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_video_widget.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/highlighted_text.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/model/wk_video_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ChatPinnedMessageWidget extends StatelessWidget {
  final PinnedMessage message;
  final VoidCallback onClose;
  final Function(int)? onTipsOrderSeq;

  const ChatPinnedMessageWidget({
    super.key,
    required this.message,
    required this.onClose,
    this.onTipsOrderSeq,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        if (onTipsOrderSeq != null) {
          onTipsOrderSeq!(message.messageSeq);
        }
      },
      child: Container(
        margin: const EdgeInsets.only(left: 12, right: 12, top: 8),
        padding: const EdgeInsets.only(left: 12, right: 0, top: 8, bottom: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(3),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
              ),
              child: Image.asset(
                ImagePath.ic_msg_top,
                width: 16,
                height: 16,
                fit: BoxFit.contain,
                color: Colors.white,
              ),
            ),
            SizedBox(width: 8),
            FutureBuilder<Widget?>(
              future: _buildMediaContent(),
              builder: (context, snapshot) {
                return Container(
                  child: snapshot.data,
                );
              },
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  FutureBuilder<String?>(
                    future: _messageContent(),
                    builder: (context, snapshot) {
                      return Row(
                        children: [
                          Expanded(
                            child: Text(
                              snapshot.data ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontSize: 14, color: Colors.black87),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  SizedBox(height: 2),
                  FutureBuilder<String?>(
                    future: _pinnedDisplay(),
                    builder: (context, snapshot) {
                      return GestureDetector(
                          child: HighlightedText(
                            text: context.l10n.pinnedBy(snapshot.data ?? ''),
                            keyword: snapshot.data ?? '',
                            highlightColor: Theme.of(context).primaryColor,
                            textStyle: TextStyle(
                                fontSize: 12, color: Colors.grey[600]),
                          ),
                          onTap: () {
                            UserInfoPage.open(channelID: message.createdBy);
                          });
                    },
                  ),
                ],
              ),
            ),
            IconButton(
              icon: Image.asset(
                ImagePath.ic_close,
                width: 16,
                height: 16,
              ),
              onPressed: onClose,
              padding: EdgeInsets.all(12),
            ),
          ],
        ),
      ),
    );
  }

  Future<Widget> _buildMediaContent() async {
    WKMsg? msg =
        await WKIM.shared.messageManager.getWithMessageID(message.messageId);
    if (msg?.messageContent is WKVideoContent) {
      return Row(
        children: [
          VideoContentWidget(
              channelID: msg!.channelID,
              channelType: msg.channelType,
              messageID: msg.messageID,
              width: 40,
              height: 40,
              videoContent: msg.messageContent as WKVideoContent,
              showDuration: false),
          SizedBox(width: 8)
        ],
      );
    } else if (msg?.messageContent is WKImageContent) {
      return Row(
        children: [
          ImageContentWidget(
              width: 40,
              height: 40,
              imageContent: msg?.messageContent as WKImageContent),
          SizedBox(width: 8)
        ],
      );
    }
    return SizedBox(width: 2);
  }

  Future<String?> _messageContent() async {
    WKMsg? wkMsg =
        await WKIM.shared.messageManager.getWithMessageID(message.messageId);
    final String fromName = await wkMsg?.fromNameDisplay ?? "";
    var content = wkMsg?.messageContent?.displayText() ?? "";
    if (wkMsg?.messageContent is WKFileContent) {
      content += " ${(wkMsg?.messageContent as WKFileContent).name}";
    }

    return "$fromName : $content";
  }

  Future<String> _pinnedDisplay() async {
    String? displayName;
    if (message.createdBy == CacheHelper.uid) {
      return globalContext?.l10n.you ?? "you";
    }
    final channel = await WKIM.shared.channelManager
        .getChannel(message.createdBy, WKChannelType.personal);
    displayName = channel?.displayName ?? "";
    if (channel == null) {
      final channelInfo = await HttpUtils.getChannelInfo(
          message.channelId, WKChannelType.personal);
      displayName = channelInfo?.remark ?? channelInfo?.name;
    }
    return displayName ?? "";
  }
}
