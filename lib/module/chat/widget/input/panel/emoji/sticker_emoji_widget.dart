import 'dart:io';

import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/user.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/sticker_info.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_utils.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:uuid/uuid.dart';

class StickerEmojiWidget extends StatefulWidget {
  final Function(StickerInfo)? onStickerEmojiTap;

  const StickerEmojiWidget({super.key, this.onStickerEmojiTap});

  @override
  State createState() => _StickerEmojiWidgetState();
}

class _StickerEmojiWidgetState extends State<StickerEmojiWidget>
    with SingleTickerProviderStateMixin {
  final int ADD_STICKER_ID = -1;
  final int DELETE_STICKER_ID = -2;
  var isDeleteMode = false;
  static List<StickerInfo> _stickers = [];

  @override
  void initState() {
    super.initState();
    _loadStickers();
  }

  Future<void> _loadStickers() async {
    final stickers = await UserApi(MyHttp.dio).stickers();
    setState(() {
      _stickers = stickers;
    });
  }

  Future<void> _uploadSticker() async {
    List<File> files = await ImageUtils.pickImages(context, compress: true);
    var result = await HttpUtils.uploadSticker(files);
    if (result) {
      await _loadStickers();
    }
  }

  Future<void> _deleteSticker(StickerInfo sticker) async {
    EasyLoadingHelper.show(onAction: () async {
      try {
        await UserApi(MyHttp.dio).deleteSticker(sticker.id.toString());
        showToast(context.l10n.globalDeleteSuccess);
        await _loadStickers();
      } catch (e) {
        showToast(context.l10n.deleteFailed);
      }
    });
  }

  void _onStickerTap(StickerInfo sticker) {
    widget.onStickerEmojiTap?.call(sticker);
  }

  @override
  Widget build(BuildContext context) {
    return _buildStickerPanel();
  }

  Widget _buildStickerPanel() {
    var stickers = [];
    stickers.add(StickerInfo(uid: "", filePath: "", id: ADD_STICKER_ID));

    if (_stickers.isNotEmpty) {
      stickers.addAll(_stickers);
      stickers.add(StickerInfo(uid: "", filePath: "", id: DELETE_STICKER_ID));
    }
    return GridView.builder(
      padding: EdgeInsets.all(8),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: stickers.length,
      itemBuilder: (context, index) {
        final sticker = stickers[index];
        if (sticker.id == ADD_STICKER_ID) {
          return GestureDetector(
            onTap: () => _uploadSticker(),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: DColor.greyE7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.add),
            ),
          );
        } else if (sticker.id == DELETE_STICKER_ID) {
          return GestureDetector(
            onTap: () {
              setState(() {
                isDeleteMode = !isDeleteMode;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: DColor.greyE7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: isDeleteMode
                  ? Center(
                      child: Text(
                      context.l10n.finish,
                      style: TextStyles.fontSize13Normal,
                    ))
                  : Icon(Icons.remove),
            ),
          );
        }
        return GestureDetector(
          onTap: () => _onStickerTap(sticker),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: DColor.greyE7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: [
                Positioned.fill(
                    child: ExtendedImage.network(
                        shape: BoxShape.rectangle,
                        borderRadius: BorderRadius.circular(8),
                        clipBehavior: Clip.antiAlias,
                        CommonHelper.getFileUrl(sticker.filePath),
                        fit: BoxFit.cover)),
                Positioned(
                    right: 0,
                    top: 0,
                    child: isDeleteMode
                        ? GestureDetector(
                            onTap: () {
                              _showDeleteDialog(sticker);
                            },
                            child: Container(
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  color: Colors.grey, shape: BoxShape.circle),
                              child: Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                          )
                        : Container())
              ],
            ),
          ),
        );
      },
    );
  }

  void _showDeleteDialog(StickerInfo sticker) async {
    var result = (await DialogUtils.showConfirmDialog(
            context, context.l10n.globalDeleteConfirm)) ??
        false;
    if (result) {
      _deleteSticker(sticker);
    }
  }
}
