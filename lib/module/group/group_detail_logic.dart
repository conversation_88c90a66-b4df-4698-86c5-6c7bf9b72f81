import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/group_info_keys.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/eventbus/group_exit_event.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/group.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/model/request/group_member_request.dart';
import 'package:and/module/chat/chat_logic.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:and/module/contact/choose/choose_contact_page.dart';
import 'package:and/module/group/delete/delete_group_member_page.dart';
import 'package:and/module/group/group_detail_page.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'name/change_group_name_page.dart';
import 'notice/change_group_notice_page.dart';

class GroupDetailLogic extends ListController<WKChannelMember> {
  final String key = 'group_detail';
  final GroupDetailArgument argument;
  late var channel = WKChannel(argument.groupNo, WKChannelType.group).obs;
  late var currentMember = Rx<WKChannelMember?>(null);

  final filterKey = "".obs;
  final filterMembers = RxList<WKChannelMember>();
  final groupManagers = RxList<WKChannelMember>();

  GroupDetailLogic({required this.argument});

  @override
  void onReady() async {
    super.onReady();

    startRefresh();
    _initListener();
  }

  void startRefresh() {
    EasyLoadingHelper.show(onAction: () async {
      await _refreshBase();
      await refreshData();
    });
  }

  Future<void> _refreshBase() async {
    await _refreshChannelInfo();
    await _refreshMemberInfo();
    await refreshManagerInfo();
  }

  Future<void> refreshManagerInfo() async {
    var members = await WKIM.shared.channelMemberManager
            .getMembers(argument.groupNo, WKChannelType.group) ??
        [];
    groupManagers.value =
        members.where((m) => m.role == WKChannelMemberRole.manager).toList();
  }

  Future<void> _refreshChannelInfo() async {
    channel.value = (await WKIM.shared.channelManager
            .getChannel(argument.groupNo, WKChannelType.group)) ??
        WKChannel(argument.groupNo, WKChannelType.group);

    await WKIM.shared.channelManager
        .fetchChannelInfo(argument.groupNo, WKChannelType.group);
  }

  Future<void> _refreshMemberInfo() async {
    currentMember.value = await WKIM.shared.channelMemberManager.getMember(
        argument.groupNo, WKChannelType.group, CacheHelper.uid ?? '');
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<WKChannelMember>?> loadData() async {
    var members = await WKIM.shared.channelMemberManager
            .getMembers(argument.groupNo, WKChannelType.group) ??
        [];
    members.sort((a, b) {
      return b.role - a.role;
    });
    var membersFilter = <WKChannelMember>[];
    if (filterKey.value.isEmpty) {
      membersFilter.addAll(list);
    } else {
      membersFilter
          .addAll(list.where((e) => e.displayName.contains(filterKey.value)));
    }
    filterMembers.value = membersFilter;

    return members;
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      print("刷新channel资料事件");
      if (channel.channelID == argument.groupNo &&
          channel.channelType == WKChannelType.group) {
        this.channel.value = channel;
        refreshData();
      }
    });

    // 监听刷新member事件
    WKIM.shared.channelMemberManager.addOnRefreshMemberListener(key,
        (member, isEnd) {
      print("刷新member事件");

      if (member.channelID == argument.groupNo) {
        if (member.memberUID == CacheHelper.uid) {
          _refreshMemberInfo();
        }

        refreshData();
      }
    });

    // 监听新增member事件
    WKIM.shared.channelMemberManager.addOnNewMemberListener(key, (members) {
      print("新增member事件");

      var isAdd = members.any((e) => e.channelID == argument.groupNo);
      if (isAdd) {
        refreshData();
      }
    });

    // 监听删除member事件
    WKIM.shared.channelMemberManager.addOnDeleteMemberListener(key, (members) {
      print("删除member事件");

      var isDelete = members.any((e) => e.channelID == argument.groupNo);
      if (isDelete) {
        refreshData();
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
    WKIM.shared.channelMemberManager.removeRefreshMemberListener(key);
    WKIM.shared.channelMemberManager.removeNewMemberListener(key);
    WKIM.shared.channelMemberManager.removeDeleteMemberListener(key);
  }

  Future<void> addGroupMembers() async {
    var groupNo = argument.groupNo;

    var isGroupEnable = channel.value.isGroupEnable;
    if (!isGroupEnable) return;

    var list = await WKIM.shared.channelMemberManager
            .getMembers(groupNo, WKChannelType.group) ??
        [];
    var memberIds = list.map((e) => e.memberUID).toList();
    var chooseUsers =
        await ChooseContactPage.open(unSelectChannelIds: memberIds) ??
            <WKChannel>[];
    if (chooseUsers.isEmpty) {
      return;
    }
    if (currentMember.value?.isDeleted == 1) {
      return;
    }
    EasyLoadingHelper.show(onAction: () async {
      try {
        var ids = chooseUsers.map((e) => e.channelID).toList();
        var isInvite = channel.value.invite == 1;
        if (isInvite) {
          //群主或者管理员，可以直接加人
          if (currentMember.value?.role == WKChannelMemberRole.admin ||
              currentMember.value?.role == WKChannelMemberRole.manager) {
            isInvite = false;
          }
        }
        if (isInvite) {
          await GroupApi(MyHttp.dio)
              .inviteGroupMembers(groupNo, GroupMemberRequest(members: ids));

          var context = globalContext;
          if (context != null) {
            showToast(context.l10n.invitationSent);
          }
        } else {
          await GroupApi(MyHttp.dio)
              .addGroupMembers(groupNo, GroupMemberRequest(members: ids));
        }
      } catch (e) {
        print('添加成员失败');
      }
    });
  }

  void deleteGroupMembers() {
    DeleteGroupMembersPage.open(groupNo: argument.groupNo);
  }

  void search(String key) {
    filterKey.value = key;
    refreshData();
  }

  Future<bool> updateGroupForbidden(String key, int value) async {
    var isGroupEnable = channel.value.isGroupEnable;
    if (!isGroupEnable) return false;

    var result = false;
    await EasyLoadingHelper.show(onAction: () async {
      result = await HttpUtils.updateGroupForbidden(argument.groupNo, value);
    });

    return result;
  }

  Future<bool> updateGroupSetting(String key, dynamic value) async {
    var isGroupEnable = channel.value.isGroupEnable;
    if (!isGroupEnable) return false;

    var result = false;
    await EasyLoadingHelper.show(onAction: () async {
      result = await HttpUtils.updateGroupSetting(argument.groupNo, key, value);
    });

    return result;
  }

  Future<bool> updateGroupInfo(String key, dynamic value) async {
    var isGroupEnable = channel.value.isGroupEnable;
    if (!isGroupEnable) return false;

    var result = false;
    await EasyLoadingHelper.show(onAction: () async {
      result = await HttpUtils.updateGroupInfo(argument.groupNo, key, value);
    });

    return result;
  }

  void clearHistory(BuildContext context) async {
    bool clearBoth = false;
    bool? result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(context.l10n.clearHistory),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  context.l10n.clearHistoryTip(channel.value.displayName),
                ),
                Row(
                  children: [
                    Checkbox(
                      value: clearBoth,
                      onChanged: (value) {
                        setState(() {
                          clearBoth = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Text(context.l10n.clearHistoryBoth),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(context.l10n.globalConfirm),
          ),
        ],
      ),
    );
    if (result == true) {
      _clearChannelHistory(clearBoth);
    }
  }

  Future<void> _clearChannelHistory(bool clearBoth) async {
    EasyLoadingHelper.show(onAction: () async {
      await HttpUtils.offsetMsg(argument.groupNo, WKChannelType.group,
          both: clearBoth);
      await WKIM.shared.messageManager
          .clearWithChannel(argument.groupNo, WKChannelType.group);
    });
  }

  void searchHistory(BuildContext context) {
    GlobalSearchPage.open(
        searchType: GlobalSearchType.channelMsg,
        channel: WKChannel(argument.groupNo, WKChannelType.group));
  }

  void exitGroup(BuildContext context) async {
    var result = (await DialogUtils.showConfirmDialog(
            context, context.l10n.exitGroupTips,
            title: context.l10n.deleteGroup)) ??
        false;
    if (result) {
      EasyLoadingHelper.show(onAction: () async {
        var result = await HttpUtils.exitGroup(argument.groupNo);
        if (result) {
          await WKIM.shared.messageManager
              .clearWithChannel(argument.groupNo, WKChannelType.group);
          await HttpUtils.offsetMsg(argument.groupNo, WKChannelType.group);
          await WKIM.shared.conversationManager
              .deleteMsg(argument.groupNo, WKChannelType.group);

          eventBus.fire(GroupExitEvent(channel.value));
          Get.back();
        }
      });
    }
  }

  void changeName(BuildContext context) async {
    var isGroupEnable = channel.value.isGroupEnable;
    if (!isGroupEnable) return;
    if (currentMember.value?.role == WKChannelMemberRole.normal) {
      DialogUtils.showAlertDialog(context, context.l10n.editGroupNotice);
      return;
    }
    Get.to(ChangeGroupNamePage(tag: argument.getTag()));
  }

  void changeNotice(BuildContext context) async {
    var isGroupEnable = channel.value.isGroupEnable;
    if (!isGroupEnable) return;
    if (currentMember.value?.role == WKChannelMemberRole.normal) {
      DialogUtils.showAlertDialog(context, context.l10n.editGroupNotice);
      return;
    }
    await Get.to(ChangeGroupNoticePage(tag: argument.getTag()));
  }

  void changeNameInGroup(BuildContext context) async {
    var isGroupEnable = channel.value.isGroupEnable;
    if (!isGroupEnable) return;

    var result = (await DialogUtils.showInputDialog(context,
            title: context.l10n.myRemarkNameInGroup,
            summary: context.l10n.updateInGroupName,
            defaultValue: currentMember.value?.displayName ?? '')) ??
        '';
    if (result.isNotEmpty) {
      EasyLoadingHelper.show(onAction: () async {
        await HttpUtils.updateGroupMemberInfo(
            argument.groupNo, CacheHelper.uid ?? '', "remark", result);

        var member = currentMember.value;
        if (member != null) {
          member.memberRemark = result;
          WKIM.shared.channelMemberManager.saveOrUpdateList([member]);
        }
      });
    }
  }

  void changeAnonymous(bool value) async {
    if (!value) {
      updateAnonymousSetting(value);
      return;
    }
    var context = globalContext!;
    bool clearBoth = false;
    bool? result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(context.l10n.enableAnonymousTip),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  context.l10n.clearHistoryTip(channel.value.displayName),
                ),
                Row(
                  children: [
                    Checkbox(
                      value: clearBoth,
                      onChanged: (value) {
                        setState(() {
                          clearBoth = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Text(context.l10n.clearHistoryBoth),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(context.l10n.turnOn),
          ),
        ],
      ),
    );
    if (result == true) {
      EasyLoadingHelper.show(onAction: () async {
        await updateAnonymousSetting(value);
        if (clearBoth) {
          await _clearChannelHistory(clearBoth);
        }
      });
    }
  }

  Future<void> updateAnonymousSetting(bool value) async {
    var result =
        await updateGroupSetting(GroupInfoKeys.anonymous, value ? 1 : 0);
    if (result) {
      channel.value.anonymous = value ? 1 : 0;
      WKIM.shared.channelManager.addOrUpdateChannel(channel.value);
    }
  }
}
