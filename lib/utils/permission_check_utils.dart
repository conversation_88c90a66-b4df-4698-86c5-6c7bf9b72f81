import 'dart:io';

import 'package:flutter/services.dart';

class PermissionCheckUtils {
  static const MethodChannel _channel = MethodChannel('permission_check');
  static const String foregroundServicePermission = "android.permission.FOREGROUND_SERVICE";
  static const String dataSyncPermission = "android.permission.FOREGROUND_SERVICE_DATA_SYNC";
  static const String microphonePermission = "android.permission.FOREGROUND_SERVICE_MICROPHONE";

  static Future<bool> checkPermission(String permission) async {
    if (Platform.isIOS) {
      return false;
    }
    bool result = false;

    try {
      result = (await _channel.invokeMethod(
            'hasPermissionInManifest',
            {'permission': permission},
          )) ??
          false;
    } catch (e) {
      print("Permission check failed: $e");
    }
    return result;
  }
}
