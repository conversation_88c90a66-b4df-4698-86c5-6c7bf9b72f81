import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:native_string_res/native_string_res.dart';
import 'dart:async';

/// Dynamic [FirebaseOptions] that can be configured at runtime through dart-define parameters.
///
/// This class reads Firebase configuration from dart-define parameters passed during build time.
/// If the parameters are not available, it will return null, and the app should fall back to
/// the default Firebase options.
///
/// Example usage:
/// ```dart
/// await Firebase.initializeApp(
///   options: DynamicFirebaseOptions.current ?? DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DynamicFirebaseOptions {
  static Future<FirebaseOptions?> get current async {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return await android;
      default:
        return null;
    }
  }

  static Future<String?> getValue(String key) async {
    String? value;
    try {
      value = await NativeStringRes().getValue(
        androidName: key,
        iOSName: key,
        iOSPlistName: 'Info',
      );
    } catch (e) {
      print('Error getting value for $key: $e');
    }

    return value;
  }

  /// Android Firebase options from NativeStringRes.
  static Future<FirebaseOptions?> get android async {
    final apiKey = await getValue('google_api_key');
    final appId = await getValue('google_app_id');
    final messagingSenderId = await getValue('gcm_defaultSenderId');
    final projectId = await getValue('project_id');
    final storageBucket = await getValue('google_storage_bucket');

    if (apiKey == null ||
        appId == null ||
        messagingSenderId == null ||
        projectId == null) {
      return null;
    }

    return FirebaseOptions(
      apiKey: apiKey,
      appId: appId,
      messagingSenderId: messagingSenderId,
      projectId: projectId,
      storageBucket: storageBucket ?? '',
    );
  }
}
