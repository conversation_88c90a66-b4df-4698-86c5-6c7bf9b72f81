import 'package:and/http/http_config.dart';
import 'package:and/model/addr.dart';
import 'package:and/model/friend_apply_count.dart';
import 'package:and/model/invite_code.dart';
import 'package:and/model/online_user_and_device.dart';
import 'package:and/model/request/add_sticker_request.dart';
import 'package:and/model/request/email_register_request.dart';
import 'package:and/model/request/phone_register_request.dart';
import 'package:and/model/request/pwd_forget_mail_request.dart';
import 'package:and/model/request/pwd_forget_sms_request.dart';
import 'package:and/model/request/send_forget_pwd_sms_request.dart';
import 'package:and/model/request/send_sms_request.dart';
import 'package:and/model/request/talk_to_any_one_request.dart';
import 'package:and/model/request/update_email_request.dart';
import 'package:and/model/request/update_phone_request.dart';
import 'package:and/model/request/upload_devicetoken_request.dart';
import 'package:and/model/request/user_login_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:and/model/response/send_sms_response.dart';
import 'package:and/model/search_user.dart';
import 'package:and/model/sticker_info.dart';
import 'package:and/model/user_badge.dart';
import 'package:and/model/user_info.dart';
import 'package:and/model/user_login_info.dart';
import 'package:and/model/user_qr.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'user.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class UserApi {
  factory UserApi(Dio dio, {String? baseUrl}) = _UserApi;

  /// 用户密码 登录
  @POST("/user/login")
  Future<UserLoginInfo> login(@Body() UserLoginRequest body);

  /// 获取用户所在IM节点信息
  @GET("/users/{uid}/im")
  Future<Addr> getImIp(@Path("uid") String uid);

  /// 获取用户信息
  @GET("/users/{uid}")
  Future<UserInfo> getUserInfo(
      @Path("uid") String uid, @Query("group_no") String? groupNo);

  /// 修改对某个好友的设置
  @PUT("/users/{uid}/setting")
  Future<CommonResponse> updateUserSetting(
    @Path("uid") String uid,
    @Body() Map<String, dynamic> jsonObject,
  );

  /// 修改登录用户设置
  @PUT("/user/my/setting")
  Future<CommonResponse> updateMySetting(
    @Body() Map<String, dynamic> jsonObject,
  );

  /// 用户在线列表（我的设备和我的好友）
  @GET("/user/online")
  Future<OnlineUserAndDevice> online();

  /// 获取用户qrcode
  @GET("/user/qrcode")
  Future<UserQr> userQr();

  /// 搜索用户信息
  @GET("/user/search")
  Future<SearchUser> searchUser(@Query("keyword") String keyword);

  /// 修改登录用户信息
  @PUT("/user/current")
  Future<CommonResponse> update(@Body() Map<String, dynamic> body);

  /// 邮箱注册用户
  @POST("/user/email_register")
  // @Extra({"showErrorMsg": true})
  Future<CommonResponse> emailRegister(@Body() EmailRegisterRequest body);

  /// 手机号注册用户
  @POST("/user/register")
  Future<CommonResponse> phoneRegister(@Body() PhoneRegisterRequest body);

  /// 重置密码
  @POST("/user/pwdforget_email")
  Future<CommonResponse> pwdForgetEmail(@Body() PwdForgetMailRequest body);

  /// 重置密码
  @POST("/user/pwdforget")
  Future<CommonResponse> pwdForget(@Body() PwdForgetSmsRequest body);

  /// 添加到黑名单
  @POST("/user/blacklist/{uid}")
  Future<CommonResponse> addBlackList(@Path("uid") String uid);

  /// 移除黑名单
  @DELETE("/user/blacklist/{uid}")
  Future<CommonResponse> removeBlackList(@Path("uid") String uid);

  /// 好友申请数量
  @GET("/user/reddot/friendApply")
  Future<FriendApplyCount> friendApplyCount();

  /// 删除申请数量
  @DELETE("/user/reddot/friendApply")
  Future<CommonResponse> deleteApplyCount();

  /// 上传device token到服务端
  @POST("/user/device_token")
  Future<CommonResponse> uploadDeviceToken(
      @Body() UploadDevicetokenRequest body);

  /// 卸载用户设备
  @DELETE("/user/device_token")
  @Extra({"showErrorMsg": true})
  Future<CommonResponse> deleteDeviceToken();

  /// 更新设备未读消息数
  @POST("/user/device_badge")
  Future<CommonResponse> updateBadge(@Body() UserBadge body);

  /// 获取邀请码
  @POST("/visitor/get_invite_code")
  Future<InviteInfo> getInviteInfo(@Body() body);

  /// 修改邀请码
  @POST("/visitor/custom_invite_code")
  @Extra({"showErrorMsg": true})
  Future<InviteInfo> customInviteInfo(@Body() Map<String, dynamic> body);

  /// 注销用户
  @DELETE("/user/destroy/{code}")
  Future<CommonResponse> destoryAccount(@Path("code") String code);

  /// 获取注销账号短信
  @POST("/user/sms/destroy")
  Future<CommonResponse> destoryAccountSMS();

  /// 发送短信验证码
  @POST("/user/sms/registercode")
  Future<SMSResponse> sendSMS(@Body() SendSMSRequest body);

  /// 发送短信验证码
  @POST("/user/sms/forgetpwd")
  Future<SMSResponse> sendForgetPwdSMS(@Body() SendForgetPwdSMSRequest body);

  /// 更新手机号
  @POST("/user/update_phone")
  Future<CommonResponse> updatePhone(@Body() UpdatePhoneRequest body);

  /// 绑定邮箱
  @POST("/user/update_email")
  Future<CommonResponse> updateEmail(@Body() UpdateEmailRequest body);

  /// 初始化用户聊天窗
  @POST("/user/super/talkToAnyOne")
  Future<CommonResponse> talkToAnyOne(@Body() TalkToAnyOneRequest body);

  /// 用户自定义表情包列表
  @GET("/user/stickers")
  Future<List<StickerInfo>> stickers();

  /// 删除自定义表情包
  @DELETE("/user/stickers/{id}")
  Future<CommonResponse> deleteSticker(@Path("id") String id);

  /// 增加自定义表情包
  @POST("/user/stickers")
  Future<CommonResponse> addStickers(@Body() AddStickerRequest body);
}
