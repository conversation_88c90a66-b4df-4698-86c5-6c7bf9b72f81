import 'package:and/http/http_config.dart';
import 'package:and/model/app_version.dart';
import 'package:and/model/country.dart';
import 'package:and/model/scan_result.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'common.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class CommonApi {
  factory CommonApi(Dio dio, {String? baseUrl}) = _CommonApi;

  /// 国家及地区列表
  @GET("/common/countries")
  Future<List<Country>> countries();

  /// 扫描信息
  @GET("{url}")
  Future<ScanResult> getScanResult({
    @Path() String? url,
  });

  /// 获取最新版本
  @GET("/common/appversion/{os}/{version}")
  Future<AppVersion> checkVersion({
    @Path("os") String? os,
    @Path("version") String? version,
  });
}
